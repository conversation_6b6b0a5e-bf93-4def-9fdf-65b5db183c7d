"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
function activate(context) {
    console.log('Neontractor Settings extension is now active!');
    // 注册设置 NeonTractor 初始设置的命令
    let disposable = vscode.commands.registerCommand('neontractor-settings.setNeonTractorInitialSettings', async () => {
        try {
            // 获取用户配置
            const config = vscode.workspace.getConfiguration();
            // 设置工作台颜色主题为 "Neon City"
            await config.update('workbench.colorTheme', 'Neon City', vscode.ConfigurationTarget.Global);
            // 显示成功消息
            vscode.window.showInformationMessage('Successfully set theme to Neon City!');
        }
        catch (error) {
            // 显示错误消息
            vscode.window.showErrorMessage(`Failed to set theme: ${error}`);
        }
    });
    context.subscriptions.push(disposable);
    // 可选：扩展激活时自动设置主题
    // 如果你希望在扩展激活时自动设置主题，可以取消注释下面的代码
    /*
    vscode.commands.executeCommand('neontractor-settings.setNeonCityTheme');
    */
}
exports.activate = activate;
function deactivate() {
    console.log('Neontractor Settings extension is now deactivated!');
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map