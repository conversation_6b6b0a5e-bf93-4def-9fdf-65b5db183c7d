# Compiled output
out/
*.js
*.js.map

# Node.js dependencies
node_modules/

# VSCode extension package
*.vsix

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# VS Code settings (optional - uncomment if you want to ignore)
# .vscode/

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Temporary files
*.tmp
*.temp

# IDE files
.idea/
*.swp
*.swo

# Build artifacts
dist/
build/
