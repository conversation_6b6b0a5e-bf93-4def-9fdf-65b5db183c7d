# Neontractor Settings

A VSCode extension that applies NeonTractor initial settings configuration.

## Features

- **Automatic Setup**: Automatically applies settings when the extension is first installed
- Sets up a comprehensive VSCode configuration
- Applies multiple settings including theme, icons, security, and editor preferences
- Easy setup for a consistent development environment
- One-time execution to avoid repeated configuration

## Usage

### Automatic Setup (Recommended)

The extension will automatically apply all settings when it's first installed and VSCode starts up. No manual action required!

### Manual Setup

If you need to reapply settings manually:

1. Open the Command Palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
2. Type "Set NeonTractor Initial Settings" and select the command
3. All settings will be applied to your VSCode workspace



## Commands

- `Set NeonTractor Initial Settings`: Applies the complete NeonTractor settings configuration

## Settings Applied

The extension configures the following VSCode settings:

- **Theme & Icons**:
  - `workbench.colorTheme`: "Neon City"
  - `workbench.iconTheme`: "vscode-icons"

- **Extensions & Security**:
  - `roo-cline.settingsButtonEnabled`: true
  - `extensions.verifySignature`: false
  - `extensions.ignoreRecommendations`: true
  - `security.workspace.trust.untrustedFiles`: "open"

- **Editor & Explorer**:
  - `explorer.confirmDelete`: true
  - `diffEditor.renderSideBySide`: false
  - `diffEditor.renderIndicators`: true
  - `diffEditor.ignoreTrimWhitespace`: true

- **Workbench**:
  - `workbench.editorAssociations`: Sets default editor for .md and .markdown files
  - `workbench.secondarySideBar.defaultVisibility`: "visible"

## Requirements

- VSCode 1.74.0 or higher
- Recommended: Install the "Neon City" theme and "vscode-icons" extension for full functionality

## Installation

1. Clone or download this extension
2. Open the extension folder in VSCode
3. Press `F5` to run the extension in a new Extension Development Host window
4. In the new window, open the Command Palette and run "Set Neon City Theme"

## Development

### Setup

```bash
# Clone the repository
git clone <repository-url>
cd vscode_settings

# Install dependencies
npm install

# Compile TypeScript
npm run compile
```

### Development Workflow

```bash
# Watch for changes and auto-compile
npm run watch

# Test the extension (press F5 in VSCode)
# This will open a new Extension Development Host window
```

## Packaging

To package the extension as a .vsix file:

```bash
npm run package
```

This will create a `neontractor-settings-0.0.1.vsix` file that can be installed in VSCode.

### Available Scripts

- `npm run compile`: Compile TypeScript to JavaScript
- `npm run watch`: Watch for changes and auto-compile
- `npm run package`: Package the extension as .vsix file
- `npm run publish`: Publish the extension to the marketplace (requires authentication)

## Release Notes

### 0.0.1

Initial release of Neontractor Settings extension.
