# Neontractor Settings

A simple VSCode extension that sets the workspace color theme to "Neon City".

## Features

- Provides a command to set the VSCode color theme to "Neon City"
- Easy one-click theme switching

## Usage

1. Open the Command Palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
2. Type "Set Neon City Theme" and select the command
3. The theme will be applied to your VSCode workspace

## Commands

- `Set Neon City Theme`: Sets the workbench color theme to "Neon City"

## Requirements

- VSCode 1.74.0 or higher
- The "Neon City" theme should be installed in your VSCode

## Installation

1. Clone or download this extension
2. Open the extension folder in VSCode
3. Press `F5` to run the extension in a new Extension Development Host window
4. In the new window, open the Command Palette and run "Set Neon City Theme"

## Development

To build and test the extension:

```bash
npm install
npm run compile
```

## Release Notes

### 0.0.1

Initial release of Neontractor Settings extension.
