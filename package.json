{"name": "neontractor-settings", "displayName": "Neontractor <PERSON>s", "description": "A VSCode extension to set NeonTractor initial settings", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "neontractor-settings.setNeonTractorInitialSettings", "title": "Set NeonTractor Initial Settings"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}