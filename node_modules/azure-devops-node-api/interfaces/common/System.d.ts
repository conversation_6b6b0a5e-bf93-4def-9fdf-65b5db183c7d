export declare enum DayOfWeek {
    /**
     * Indicates Sunday.
     */
    Sunday = 0,
    /**
     * Indicates Monday.
     */
    Monday = 1,
    /**
     * Indicates Tuesday.
     */
    Tuesday = 2,
    /**
     * Indicates Wednesday.
     */
    Wednesday = 3,
    /**
     * Indicates Thursday.
     */
    Thursday = 4,
    /**
     * Indicates Friday.
     */
    Friday = 5,
    /**
     * Indicates Saturday.
     */
    Saturday = 6
}
export declare var TypeInfo: {
    DayOfWeek: {
        enumValues: {
            "sunday": number;
            "monday": number;
            "tuesday": number;
            "wednesday": number;
            "thursday": number;
            "friday": number;
            "saturday": number;
        };
    };
};
