import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
    console.log('Neontractor Settings extension is now active!');

    // 注册设置 NeonTractor 初始设置的命令
    let disposable = vscode.commands.registerCommand('neontractor-settings.setNeonTractorInitialSettings', async () => {
        try {
            // 获取用户配置
            const config = vscode.workspace.getConfiguration();

            // 设置所有配置项
            const settings = [
                { key: 'workbench.colorTheme', value: 'Neon City' },
                { key: 'workbench.iconTheme', value: 'vscode-icons' },
                { key: 'roo-cline.settingsButtonEnabled', value: true },
                { key: 'extensions.verifySignature', value: false },
                { key: 'extensions.ignoreRecommendations', value: true },
                { key: 'security.workspace.trust.untrustedFiles', value: 'open' },
                { key: 'explorer.confirmDelete', value: true },
                { key: 'diffEditor.renderSideBySide', value: false },
                { key: 'diffEditor.renderIndicators', value: true },
                { key: 'diffEditor.ignoreTrimWhitespace', value: true },
                {
                    key: 'workbench.editorAssociations', value: {
                        "*.md": "default",
                        "*.markdown": "default"
                    }
                },
                { key: 'workbench.secondarySideBar.defaultVisibility', value: 'visible' }
            ];

            // 逐个应用设置
            for (const setting of settings) {
                await config.update(setting.key, setting.value, vscode.ConfigurationTarget.Global);
            }

            // 显示成功消息
            vscode.window.showInformationMessage('Successfully applied NeonTractor initial settings!');
        } catch (error) {
            // 显示错误消息
            vscode.window.showErrorMessage(`Failed to apply settings: ${error}`);
        }
    });

    // 注册重置并重新应用设置的命令
    let resetDisposable = vscode.commands.registerCommand('neontractor-settings.resetAndReapplySettings', async () => {
        try {
            // 重置状态标记
            await context.globalState.update('neontractor-settings.hasRun', false);

            // 重新应用设置
            await vscode.commands.executeCommand('neontractor-settings.setNeonTractorInitialSettings');

            // 重新标记为已运行
            await context.globalState.update('neontractor-settings.hasRun', true);

            vscode.window.showInformationMessage('NeonTractor settings have been reset and reapplied!');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to reset and reapply settings: ${error}`);
        }
    });

    context.subscriptions.push(disposable);
    context.subscriptions.push(resetDisposable);

    // 扩展激活时自动应用设置
    // 检查是否是首次安装并自动执行设置
    const hasRunBefore = context.globalState.get('neontractor-settings.hasRun', false);

    if (!hasRunBefore) {
        // 首次运行，自动应用设置
        vscode.commands.executeCommand('neontractor-settings.setNeonTractorInitialSettings');
        // 标记为已运行，避免重复执行
        context.globalState.update('neontractor-settings.hasRun', true);
    }
}

export function deactivate() {
    console.log('Neontractor Settings extension is now deactivated!');
}
