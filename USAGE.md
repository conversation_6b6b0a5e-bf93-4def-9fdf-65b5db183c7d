# 如何使用 Neontractor Settings 扩展

## 开发和测试

1. **在 VSCode 中打开项目**
   ```bash
   code /Users/<USER>/workspace/vscode_settings
   ```

2. **按 F5 启动扩展开发主机**
   - 这会打开一个新的 VSCode 窗口，其中加载了你的扩展

3. **在新窗口中测试扩展**
   - 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac) 打开命令面板
   - 输入 "Set Neon City Theme" 并选择该命令
   - 扩展会将你的 VSCode 主题设置为 "Neon City"

## 扩展功能

- **命令**: `Set Neon City Theme`
- **功能**: 将 VSCode 的 `workbench.colorTheme` 设置为 "Neon City"
- **配置范围**: 全局设置 (所有工作区都会应用)

## 注意事项

- 确保你的 VSCode 中已安装 "Neon City" 主题，否则设置可能不会生效
- 如果主题不存在，扩展会显示错误消息

## 自定义

如果你想修改扩展以设置其他主题或添加更多设置：

1. 编辑 `src/extension.ts` 文件
2. 修改 `config.update()` 调用中的主题名称
3. 运行 `npm run compile` 重新编译
4. 按 F5 重新测试

## 打包扩展

如果你想将扩展打包为 .vsix 文件：

1. 安装 vsce 工具：
   ```bash
   npm install -g vsce
   ```

2. 在项目根目录运行：
   ```bash
   vsce package
   ```

这会生成一个 .vsix 文件，可以通过 VSCode 的扩展管理器安装。
