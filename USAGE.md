# 如何使用 Neontractor Settings 扩展

## 开发和测试

1. **在 VSCode 中打开项目**
   ```bash
   code /Users/<USER>/workspace/vscode_settings
   ```

2. **按 F5 启动扩展开发主机**
   - 这会打开一个新的 VSCode 窗口，其中加载了你的扩展

3. **在新窗口中测试扩展**
   - 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac) 打开命令面板
   - 输入 "Set NeonTractor Initial Settings" 并选择该命令
   - 扩展会应用所有 NeonTractor 初始设置

## 扩展功能

- **命令**: `Set NeonTractor Initial Settings`
- **功能**: 应用完整的 NeonTractor 设置配置，包括：
  - 主题和图标设置
  - 扩展和安全配置
  - 编辑器和资源管理器设置
  - 工作台布局配置
- **配置范围**: 全局设置 (所有工作区都会应用)

## 应用的设置详情

执行命令后，以下设置将被应用：

### 主题和外观
- `workbench.colorTheme`: "Neon City"
- `workbench.iconTheme`: "vscode-icons"
- `workbench.secondarySideBar.defaultVisibility`: "visible"

### 扩展和安全
- `roo-cline.settingsButtonEnabled`: true
- `extensions.verifySignature`: false
- `extensions.ignoreRecommendations`: true
- `security.workspace.trust.untrustedFiles`: "open"

### 编辑器和文件管理
- `explorer.confirmDelete`: true
- `diffEditor.renderSideBySide`: false
- `diffEditor.renderIndicators`: true
- `diffEditor.ignoreTrimWhitespace`: true
- `workbench.editorAssociations`: 为 .md 和 .markdown 文件设置默认编辑器

## 注意事项

- 确保你的 VSCode 中已安装 "Neon City" 主题，否则设置可能不会生效
- 如果主题不存在，扩展会显示错误消息

## 自定义

如果你想修改扩展以设置其他主题或添加更多设置：

1. 编辑 `src/extension.ts` 文件
2. 修改 `config.update()` 调用中的主题名称
3. 运行 `npm run compile` 重新编译
4. 按 F5 重新测试

## 打包扩展

### 使用 npm 脚本打包（推荐）

项目已经配置了打包脚本，直接运行：

```bash
npm run package
```

这会：
1. 自动编译 TypeScript 代码
2. 生成 `neontractor-settings-0.0.1.vsix` 文件

### 打包并安装到 VSCode

如果你想打包后立即安装到 VSCode：

```bash
npm run package:install
```

### 可用的打包相关脚本

- `npm run package`: 打包为 .vsix 文件
- `npm run package:install`: 打包并安装到 VSCode
- `npm run publish`: 发布到 VSCode 扩展市场（需要认证）

### 手动安装 .vsix 文件

生成 .vsix 文件后，你可以：
1. 在 VSCode 中按 `Ctrl+Shift+P` (或 `Cmd+Shift+P`)
2. 输入 "Extensions: Install from VSIX..."
3. 选择生成的 .vsix 文件进行安装
